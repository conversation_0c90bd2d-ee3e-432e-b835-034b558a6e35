<?php

namespace DD\App;

use DD\App\Database\Migration;
use DD\App\Admin\AdminMenu;
use DD\App\CF7\TagHandler;
use DD\App\CF7\FormProcessor;

/**
 * Main plugin class
 */
class Plugin
{
    /**
     * Plugin instance
     *
     * @var Plugin|null
     */
    private static $instance = null;

    /**
     * Get plugin instance
     *
     * @return Plugin
     */
    public static function getInstance(): Plugin
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct()
    {
        // Private constructor
    }

    /**
     * Initialize plugin
     */
    public function init(): void
    {
        // Check if Contact Form 7 is active
        if (!$this->isContactForm7Active()) {
            add_action('admin_notices', [$this, 'showContactForm7Notice']);
            return;
        }

        // Initialize components
        $this->initializeComponents();
    }

    /**
     * Plugin activation
     */
    public function activate(): void
    {
        // Run database migration
        Migration::createTables();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Plugin deactivation
     */
    public function deactivate(): void
    {
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Check if Contact Form 7 is active
     *
     * @return bool
     */
    private function isContactForm7Active(): bool
    {
        return class_exists('WPCF7');
    }

    /**
     * Show Contact Form 7 notice
     */
    public function showContactForm7Notice(): void
    {
        echo '<div class="notice notice-error"><p>';
        echo __('GetResponse CF7 Integration wymaga aktywnej wtyczki Contact Form 7.', 'dd-gr-cf7');
        echo '</p></div>';
    }

    /**
     * Initialize plugin components
     */
    private function initializeComponents(): void
    {
        // Initialize admin menu
        new AdminMenu();
        
        // Initialize CF7 integration
        new TagHandler();
        new FormProcessor();
        
        // Load text domain
        add_action('init', [$this, 'loadTextDomain']);
        
        // Enqueue admin scripts and styles
        add_action('admin_enqueue_scripts', [$this, 'enqueueAdminAssets']);
    }

    /**
     * Load text domain for translations
     */
    public function loadTextDomain(): void
    {
        load_plugin_textdomain(
            'dd-gr-cf7',
            false,
            dirname(plugin_basename(DD_GR_CF7_PLUGIN_FILE)) . '/languages'
        );
    }

    /**
     * Enqueue admin assets
     *
     * @param string $hook Current admin page hook
     */
    public function enqueueAdminAssets(string $hook): void
    {
        // Only load on our admin pages
        if (strpos($hook, 'getresponse-cf7') === false) {
            return;
        }

        wp_enqueue_style(
            'dd-gr-cf7-admin',
            DD_GR_CF7_PLUGIN_URL . 'assets/css/admin.css',
            [],
            DD_GR_CF7_VERSION
        );

        wp_enqueue_script(
            'dd-gr-cf7-admin',
            DD_GR_CF7_PLUGIN_URL . 'assets/js/admin.js',
            ['jquery'],
            DD_GR_CF7_VERSION,
            true
        );

        // Localize script for AJAX
        wp_localize_script('dd-gr-cf7-admin', 'ddGrCf7Ajax', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dd_gr_cf7_nonce'),
            'strings' => [
                'loading' => __('Ładowanie...', 'dd-gr-cf7'),
                'error' => __('Wystąpił błąd. Spróbuj ponownie.', 'dd-gr-cf7'),
                'success' => __('Zapisano pomyślnie.', 'dd-gr-cf7'),
            ]
        ]);
    }
}
